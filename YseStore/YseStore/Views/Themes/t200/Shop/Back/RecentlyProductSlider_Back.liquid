<div class="related-product grid-products">
    <div class="section-header">
        <h2 class="section-header__title text-center h2"><span>Recently Viewed Product</span></h2>
        <p class="sub-heading">You can manage this section from store admin as describe in above section</p>
    </div>
    <div class="productSlider-style2">
        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
            <!-- start product image -->
            <div class="product-image">
                <!-- start product image -->
                <a href="product-layout1.html" class="product-img">
                    <!-- image -->
                    <img class="primary blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-16.jpg" src="{{static_path}}/assets/images/product-images/elt-p-16.jpg" alt="" title="">
                    <!-- End image -->
                    <!-- Hover image -->
                    <img class="hover blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-16-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-16-1.jpg" alt="" title="">
                    <!-- End hover image -->
                </a>
                <!-- end product image -->
                <!--Product label-->
                <div class="product-labels"><span class="lbl pr-label2">Hot</span></div>
                <!--Product label-->
                <!--Product Button-->
                <div class="button-set style1">
                    <ul>
                        <li>
                            <!--Quick View Button-->
                            <a href="#quickview-popup" title="Quick View" class="btn-icon quick-view-popup quick-view" data-bs-toggle="modal" data-bs-target="#quickview_popup">
                                <i class="icon an an-expand-arrows-alt"></i>
                                <span class="tooltip-label">{{ "web.global.quickView"|translate}}</span>
                            </a>
                            <!--End Quick View Button-->
                        </li>
                        <li>
                            <!--Wishlist Button-->
                            <div class="wishlist-btn">
                                <a class="btn-icon wishlist add-to-wishlist" href="my-wishlist.html">
                                    <i class="icon an an-heart-o"></i>
                                    <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                </a>
                            </div>
                            <!--End Wishlist Button-->
                        </li>
                    </ul>
                </div>
                <!--End Product Button-->
            </div>
            <!-- end product image -->
            <!--start product details -->
            <div class="product-details text-left">
                <!--Brand Name-->
                <div class="brand-name">Philips</div>
                <!--End Brand Name-->
                <!-- product name -->
                <div class="product-name">
                    <a href="product-layout1.html">Pack Women Hair Straightener & Dryer</a>
                </div>
                <!-- End product name -->
                <!-- product price -->
                <div class="product-price">
                    <span class="price">$99.00</span>
                </div>
                <!-- End product price -->
                <!--Product Review-->
                <div class="product-review">
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <span class="review-label"><a href="#;">1 {{ "products.goods.reviews"|translate}}</a></span>
                </div>
                <!--End Product Review-->
                <!--Cart Button-->
                <a href="#pro-addtocart-popup" title="Add to Cart" class="btn-icon btn btn-addto-cart" data-bs-toggle="modal" data-bs-target="#pro-addtocart-popup">
                    <i class="icon an an-shopping-cart"></i> <span>{{ "products.goods.addToCart"|translate}}</span>
                </a>
                <!--end Cart Button-->
            </div>
            <!-- End product details -->
        </div>
        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
            <!-- start product image -->
            <div class="product-image">
                <!-- start product image -->
                <a href="product-layout1.html" class="product-img">
                    <!-- image -->
                    <img class="primary blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-13.jpg" src="{{static_path}}/assets/images/product-images/elt-p-13.jpg" alt="" title="">
                    <!-- End image -->
                    <!-- Hover image -->
                    <img class="hover blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-13-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-13-1.jpg" alt="" title="">
                    <!-- End hover image -->
                </a>
                <!-- end product image -->
                <!--Product Button-->
                <div class="button-set style1">
                    <ul>
                        <li>
                            <!--Quick View Button-->
                            <a href="#quickview-popup" title="Quick View" class="btn-icon quick-view-popup quick-view" data-bs-toggle="modal" data-bs-target="#quickview_popup">
                                <i class="icon an an-expand-arrows-alt"></i>
                                <span class="tooltip-label">{{ "web.global.quickView"|translate}}</span>
                            </a>
                            <!--End Quick View Button-->
                        </li>
                        <li>
                            <!--Wishlist Button-->
                            <div class="wishlist-btn">
                                <a class="btn-icon wishlist add-to-wishlist" href="my-wishlist.html">
                                    <i class="icon an an-heart-o"></i>
                                    <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                </a>
                            </div>
                            <!--End Wishlist Button-->
                        </li>
                    </ul>
                </div>
                <!--End Product Button-->
            </div>
            <!-- end product image -->
            <!--start product details -->
            <div class="product-details text-left">
                <!--Brand Name-->
                <div class="brand-name">Lenovo</div>
                <!--End Brand Name-->
                <!-- product name -->
                <div class="product-name">
                    <a href="product-layout1.html">Lenovo Tab M10 FHD Plus Tablet</a>
                </div>
                <!-- End product name -->
                <!-- product price -->
                <div class="product-price">
                    <span class="price">$199.00</span>
                </div>
                <!-- End product price -->
                <!--Product Review-->
                <div class="product-review">
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <span class="review-label"><a href="#;">1 {{ "products.goods.reviews"|translate}}</a></span>
                </div>
                <!--End Product Review-->
                <!--Cart Button-->
                <a href="#pro-addtocart-popup" title="Add to Cart" class="btn-icon btn btn-addto-cart" data-bs-toggle="modal" data-bs-target="#pro-addtocart-popup">
                    <i class="icon an an-shopping-cart"></i> <span>{{ "products.goods.addToCart"|translate}}</span>
                </a>
                <!--end Cart Button-->
            </div>
            <!-- End product details -->
        </div>
        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
            <!-- start product image -->
            <div class="product-image">
                <!-- start product image -->
                <a href="product-layout1.html" class="product-img">
                    <!-- image -->
                    <img class="primary blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" alt="" title="">
                    <!-- End image -->
                    <!-- Hover image -->
                    <img class="hover blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" alt="" title="">
                    <!-- End hover image -->
                </a>
                <!-- end product image -->
                <!--Product label-->
                <div class="product-labels"><span class="lbl pr-label1">New</span></div>
                <!--Product label-->
                <!--Product Button-->
                <div class="button-set style1">
                    <ul>
                        <li>
                            <!--Quick View Button-->
                            <a href="#quickview-popup" title="Quick View" class="btn-icon quick-view-popup quick-view" data-bs-toggle="modal" data-bs-target="#quickview_popup">
                                <i class="icon an an-expand-arrows-alt"></i>
                                <span class="tooltip-label">{{ "web.global.quickView"|translate}}</span>
                            </a>
                            <!--End Quick View Button-->
                        </li>
                        <li>
                            <!--Wishlist Button-->
                            <div class="wishlist-btn">
                                <a class="btn-icon wishlist add-to-wishlist" href="my-wishlist.html">
                                    <i class="icon an an-heart-o"></i>
                                    <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                </a>
                            </div>
                            <!--End Wishlist Button-->
                        </li>
                    </ul>
                </div>
                <!--End Product Button-->
            </div>
            <!-- end product image -->
            <!--start product details -->
            <div class="product-details text-left">
                <!--Brand Name-->
                <div class="brand-name">Dell</div>
                <!--End Brand Name-->
                <!-- product name -->
                <div class="product-name">
                    <a href="product-layout1.html">DELL 22 inch Full HD IPS Panel Monitor</a>
                </div>
                <!-- End product name -->
                <!-- product price -->
                <div class="product-price">
                    <span class="price">$249.00</span>
                </div>
                <!-- End product price -->
                <!--Product Review-->
                <div class="product-review">
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <span class="review-label"><a href="#;">10 {{ "products.goods.reviews"|translate}}</a></span>
                </div>
                <!--End Product Review-->
                <!--Cart Button-->
                <a href="#pro-addtocart-popup" title="Add to Cart" class="btn-icon btn btn-addto-cart" data-bs-toggle="modal" data-bs-target="#pro-addtocart-popup">
                    <i class="icon an an-lg an-cog"></i> <span>Select Options</span>
                </a>
                <!--end Cart Button-->
            </div>
            <!-- End product details -->
        </div>
        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
            <!-- start product image -->
            <div class="product-image">
                <!-- start product image -->
                <a href="product-layout1.html" class="product-img">
                    <!-- image -->
                    <img class="primary blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-11.jpg" src="{{static_path}}/assets/images/product-images/elt-p-11.jpg" alt="" title="">
                    <!-- End image -->
                    <!-- Hover image -->
                    <img class="hover blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-11-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-11-1.jpg" alt="" title="">
                    <!-- End hover image -->
                </a>
                <!-- end product image -->
                <!--Product Button-->
                <div class="button-set style3">
                    <ul>
                        <li>
                            <!--Quick View Button-->
                            <a href="#quickview-popup" title="Quick View" class="btn-icon quick-view-popup quick-view" data-bs-toggle="modal" data-bs-target="#quickview_popup">
                                <i class="icon an an-expand-arrows-alt"></i>
                                <span class="tooltip-label">{{ "web.global.quickView"|translate}}</span>
                            </a>
                            <!--End Quick View Button-->
                        </li>
                        <li>
                            <!--Wishlist Button-->
                            <div class="wishlist-btn">
                                <a class="btn-icon wishlist add-to-wishlist" href="my-wishlist.html">
                                    <i class="icon an an-heart-o"></i>
                                    <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                </a>
                            </div>
                            <!--End Wishlist Button-->
                        </li>
                    </ul>
                </div>
                <!--End Product Button-->
            </div>
            <!-- end product image -->
            <!--start product details -->
            <div class="product-details text-left">
                <!--Brand Name-->
                <div class="brand-name">Canon</div>
                <!--End Brand Name-->
                <!-- product name -->
                <div class="product-name">
                    <a href="product-layout1.html">CyberShot DSC-W800 20.1 MP Point &amp; Shoot Camera</a>
                </div>
                <!-- End product name -->
                <!-- product price -->
                <div class="product-price">
                    <span class="old-price">$299.00</span>
                    <span class="price">$260.00</span>
                </div>
                <!-- End product price -->
                <!--Product Review-->
                <div class="product-review">
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star"></i>
                    <i class="an an-star gray-star"></i>
                    <span class="review-label"><a href="#;">8 {{ "products.goods.reviews"|translate}}</a></span>
                </div>
                <!--End Product Review-->
                <!--Cart Button-->
                <a href="#pro-addtocart-popup" title="Add to Cart" class="btn-icon btn btn-addto-cart" data-bs-toggle="modal" data-bs-target="#pro-addtocart-popup">
                    <i class="icon an an-shopping-cart"></i> <span>{{ "products.goods.addToCart"|translate}}</span>
                </a>
                <!--end Cart Button-->
            </div>
            <!-- End product details -->
        </div>
    </div>
</div>
<!-- Quickview Modal -->
<div class="modal fade" id="quickview_popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6 col-lg-6">
                        <div id="slider">
                            <!-- model thumbnail -->
                            <div id="quickView" class="carousel slide">
                                <div class="quickview-in">
                                    <!-- image slide carousel items -->
                                    <div class="carousel-inner">
                                        <!-- slide 1 -->
                                        <div class="item carousel-item active" data-bs-slide-number="0">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 1 -->
                                        <!-- slide 2 -->
                                        <div class="item carousel-item" data-bs-slide-number="1">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 2 -->
                                        <!-- slide 3 -->
                                        <div class="item carousel-item" data-bs-slide-number="2">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-9.jpg" src="{{static_path}}/assets/images/product-images/elt-p-9.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 3 -->
                                        <!-- slide 4 -->
                                        <div class="item carousel-item" data-bs-slide-number="3">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-8.jpg" src="{{static_path}}/assets/images/product-images/elt-p-8.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 4 -->
                                        <!-- slide 5 -->
                                        <div class="item carousel-item" data-bs-slide-number="4">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-14.jpg" src="{{static_path}}/assets/images/product-images/elt-p-14.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 5 -->
                                        <!-- slide 6 -->
                                        <div class="item carousel-item" data-bs-slide-number="5">
                                            <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-14-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-14-1.jpg" alt="product" title="">
                                        </div>
                                        <!-- End slide 6 -->
                                    </div>
                                    <!-- End image slide carousel items -->
                                    <!-- arrow button -->
                                    <div class="np-btns">
                                        <a class="carousel-control left" href="#quickView" data-bs-target="#quickView" data-bs-slide="prev"><i class="an an-angle-left"></i></a>
                                        <a class="carousel-control right" href="#quickView" data-bs-target="#quickView" data-bs-slide="next"><i class="an an-angle-right"></i></a>
                                    </div>
                                    <!-- End arrow button -->
                                </div>
                                <!-- model thumbnail image -->
                                <div class="model-thumbnail-img">
                                    <!-- model thumbnail slide -->
                                    <ul class="carousel-indicators list-inline">
                                        <!-- slide 1 -->
                                        <li class="list-inline-item active">
                                            <a id="carousel-selector-0" class="selected" data-bs-slide-to="0" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 1 -->
                                        <!-- slide 2 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-1" data-bs-slide-to="1" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-15-1.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 2 -->
                                        <!-- slide 3 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-2" data-bs-slide-to="2" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-9.jpg" src="{{static_path}}/assets/images/product-images/elt-p-9.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 3 -->
                                        <!-- slide 4 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-3" data-bs-slide-to="3" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-8.jpg" src="{{static_path}}/assets/images/product-images/elt-p-8.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 4 -->
                                        <!-- slide 5 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-4" data-bs-slide-to="4" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-14.jpg" src="{{static_path}}/assets/images/product-images/elt-p-14.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 5 -->
                                        <!-- slide 6 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-5" data-bs-slide-to="5" data-bs-target="#quickView">
                                                <img class="blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/elt-p-14-1.jpg" src="{{static_path}}/assets/images/product-images/elt-p-14-1.jpg" alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 6 -->
                                    </ul>
                                    <!-- End model thumbnail slide -->
                                </div>
                                <!-- End model thumbnail image -->
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6 col-lg-6">
                        <div class="product-brand"><a href="#">Charcoal</a></div>
                        <h2 class="product-title">Product Quick View Popup</h2>
                        <div class="product-review">
                            <div class="rating">
                                <i class="an an-star"></i><i class="an an-star"></i><i class="an an-star"></i><i class="an an-star"></i><i class="an an-star"></i>
                            </div>
                            <div class="reviews"><a href="#">5 {{ "products.goods.reviews"|translate}}</a></div>
                        </div>
                        <div class="product-info">
                            <div class="product-stock"> <span class="instock">In Stock</span> <span class="outstock hide">Unavailable</span> </div>
                            <div class="product-sku">SKU: <span class="variant-sku">19115-rdxs</span></div>
                        </div>
                        <div class="pricebox">
                            <span class="price old-price">$900.00</span>
                            <span class="price">$800.00</span>
                        </div>
                        <div class="sort-description">Shoplook Multipurpose Bootstrap 5 Html Template that will give you and your customers a smooth shopping experience which can be used for various kinds of stores such as fashion.. </div>
                        <form method="post" action="#" id="product_form--option" class="product-form">
                            <div class="product-options">
                                <div class="swatch clearfix swatch-1 option2">
                                    <div class="product-form__item">
                                        <label class="label">Size:<span class="required">*</span> <span class="slVariant">XS</span></label>
                                        <div class="swatch-element xs">
                                            <input class="swatchInput" id="swatch-1-xs" type="radio" name="option-1" value="XS">
                                            <label class="swatchLbl medium" for="swatch-1-xs" title="XS">XS</label>
                                        </div>
                                        <div class="swatch-element s">
                                            <input class="swatchInput" id="swatch-1-s2" type="radio" name="option-1" value="S">
                                            <label class="swatchLbl medium" for="swatch-1-s2" title="S">S</label>
                                        </div>
                                        <div class="swatch-element m">
                                            <input class="swatchInput" id="swatch-1-m" type="radio" name="option-1" value="M">
                                            <label class="swatchLbl medium" for="swatch-1-m" title="M">M</label>
                                        </div>
                                        <div class="swatch-element l">
                                            <input class="swatchInput" id="swatch-1-l" type="radio" name="option-1" value="L">
                                            <label class="swatchLbl medium" for="swatch-1-l" title="L">L</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="product-action clearfix">
                                    <div class="quantity">
                                        <div class="wrapQtyBtn">
                                            <div class="qtyField">
                                                <a class="qtyBtn minus" href="javascript:void(0);"><i class="an an-minus" aria-hidden="true"></i></a>
                                                <input type="text" id="quantityp1" name="quantity" value="1" class="product-form__input qty">
                                                <a class="qtyBtn plus" href="javascript:void(0);"><i class="an an-plus" aria-hidden="true"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="add-to-cart">
                                        <button type="button" class="btn button-cart">
                                            <span>{{ "products.goods.addToCart"|translate}}</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="wishlist-btn">
                            <a class="wishlist add-to-wishlist" href="#" title="{{ "products.goods.addToFavorites"|translate}}"><i class="icon an an-heart-o" aria-hidden="true"></i> <span>{{ "products.goods.addToFavorites"|translate}}</span></a>
                        </div>
                        <div class="share-icon">
                            <span>Share:</span>
                            <ul class="list--inline social-icons">
                                <li><a href="#"><i class="icon an an-facebook-f"></i></a></li>
                                <li><a href="#"><i class="icon an an-twitter"></i></a></li>
                                <li><a href="#"><i class="icon an an-pinterest-p"></i></a></li>
                                <li><a href="#"><i class="icon an an-instagram"></i></a></li>
                                <li><a href="#"><i class="icon an an-youtube"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Quickview Modal-->

<!-- 导入图片处理助手 -->
{% comment %}<script src="/businessJs/imageUrlHelper.js"></script>{% endcomment %}
{% comment %}{% endcomment %}
{% comment %}<script>{% endcomment %}
    {% comment %}// 页面加载完成后处理所有图片URL{% endcomment %}
    {% comment %}document.addEventListener('DOMContentLoaded', function() {{% endcomment %}
        {% comment %}// 处理最近浏览商品滑块中的所有图片{% endcomment %}
        {% comment %}const recentlyViewedImages = document.querySelectorAll('.related-product img');{% endcomment %}
        {% comment %}recentlyViewedImages.forEach(function(img) {{% endcomment %}
            {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
                {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
            {% comment %}}{% endcomment %}
            {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
                {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
            {% comment %}}{% endcomment %}
        {% comment %}});{% endcomment %}
{% comment %}{% endcomment %}
        {% comment %}// 处理快速预览模态框中的图片{% endcomment %}
        {% comment %}const quickViewImages = document.querySelectorAll('#quickview-popup img');{% endcomment %}
        {% comment %}quickViewImages.forEach(function(img) {{% endcomment %}
            {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
                {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
            {% comment %}}{% endcomment %}
            {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
                {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
            {% comment %}}{% endcomment %}
        {% comment %}});{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}</script>{% endcomment %}

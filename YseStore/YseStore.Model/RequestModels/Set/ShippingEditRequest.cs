using System;
using System.ComponentModel.DataAnnotations;

namespace YseStore.Model.RequestModels.Set
{
    /// <summary>
    /// 物流编辑请求模型
    /// </summary>
    public class ShippingEditRequest
    {
        /// <summary>
        /// 物流ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 物流名称/快递名称
        /// </summary>
        [Required(ErrorMessage = "物流名称不能为空")]
        public string Express { get; set; }

        /// <summary>
        /// 使用条件 (0:没有限制 1:产品总重量 2:产品总数量 3:产品总价)
        /// </summary>
        [Required]
        public int UseCondition { get; set; }

        /// <summary>
        /// 最小重量/数量/价格
        /// </summary>
        public decimal MinWeight { get; set; }

        /// <summary>
        /// 最大重量/数量/价格
        /// </summary>
        public decimal MaxWeight { get; set; }

        /// <summary>
        /// 体积重系数
        /// </summary>
        public int VolumeWeightCoefficient { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public string WeightUnit { get; set; } = "kg";

        /// <summary>
        /// 是否启用
        /// </summary>
        public byte? IsUsed { get; set; } = 0;

        /// <summary>
        /// 计费方式 (是否按重量区域计费)
        /// </summary>
        public byte? IsWeightArea { get; set; } = 0;

        /// <summary>
        /// 标准ID
        /// </summary>
        public ushort SId { get; set; }
    }
}